"use client";

import React, { useEffect, useRef, useState } from "react";
import { DropdownButtonPropsI, DropdownOptionI } from "./types";
import { DROPDOWN_BUTTON_VARIANTS } from "./consts";
import { cn } from "@/utils/class-merge";
import { ChevronDownIcon } from "@/assets/images/svgs/common";
import Spinner from "../Spinner";

const DropdownButton: React.FC<DropdownButtonPropsI> = ({
  label,
  options,
  variant = "primary",
  className = "",
  disabled = false,
  isLoading = false,
  placeholder,
  onOptionSelect,
  dropdownClassName = "",
  buttonClassName = "",
  optionClassName = "",
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const variantClasses = DROPDOWN_BUTTON_VARIANTS[variant];

  const toggleDropdown = () => {
    if (!disabled && !isLoading) {
      setIsOpen((prev) => !prev);
    }
  };

  const handleOptionClick = (option: DropdownOptionI) => {
    if (!option.disabled) {
      option.onClick?.();
      onOptionSelect?.(option);
      setIsOpen(false);
    }
  };

  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleOutsideClick);
    return () => document.removeEventListener("mousedown", handleOutsideClick);
  }, []);

  return (
    <div ref={dropdownRef} className={cn("relative inline-block", className)}>
      <button
        onClick={toggleDropdown}
        disabled={disabled || isLoading}
        className={cn(
          "relative inline-flex items-center justify-center gap-2 px-6 py-3 text-base font-medium rounded-full border transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
          variantClasses,
          (disabled || isLoading) && "cursor-not-allowed opacity-50",
          buttonClassName,
        )}
        {...props}
      >
        <span
          className={cn("flex items-center gap-2", isLoading && "opacity-0")}
        >
          {label}
          <div
            className={cn(
              "transition-transform duration-200",
              isOpen && "transform rotate-180",
            )}
          >
            <ChevronDownIcon />
          </div>
        </span>

        {isLoading && (
          <span className="absolute inset-0 flex items-center justify-center">
            <Spinner
              size={20}
              colorClass={
                variant === "primary" ? "text-white" : "text-primary-600"
              }
            />
          </span>
        )}
      </button>

      {isOpen && !disabled && !isLoading && (
        <div
          className={cn(
            "absolute right-0 z-50 mt-2 min-w-[200px] bg-white border border-neutral-300 rounded-lg shadow-lg max-h-60 overflow-auto",
            dropdownClassName,
          )}
        >
          <ul className="py-1">
            {options.length === 0 ? (
              <li className="px-4 py-2 text-neutral-500 text-sm">
                {placeholder || "No options available"}
              </li>
            ) : (
              options.map((option) => (
                <li key={option.id}>
                  <button
                    onClick={() => handleOptionClick(option)}
                    disabled={option.disabled}
                    className={cn(
                      "w-full text-left px-4 py-2 text-sm transition-colors duration-150 flex items-center gap-2",
                      option.disabled
                        ? "text-neutral-400 cursor-not-allowed"
                        : "text-neutral-950 hover:bg-neutral-100 cursor-pointer",
                      optionClassName,
                    )}
                  >
                    {option.icon && (
                      <span className="flex-shrink-0">{option.icon}</span>
                    )}
                    <span className="truncate">{option.label}</span>
                  </button>
                </li>
              ))
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

DropdownButton.displayName = "DropdownButton";

export default DropdownButton;
