import { ButtonHTMLAttributes, ReactNode } from "react";

export type DropdownOptionI = {
  id: string;
  label: string;
  icon?: ReactNode;
  disabled?: boolean;
  onClick?: () => void;
};

export type DropdownButtonVariant = "primary" | "ghost" | "outline";

export type DropdownButtonPropsI = Omit<
  ButtonHTMLAttributes<HTMLButtonElement>,
  "onClick"
> & {
  label: string;
  options: DropdownOptionI[];
  variant?: DropdownButtonVariant;
  className?: string;
  disabled?: boolean;
  isLoading?: boolean;
  placeholder?: string;
  onOptionSelect?: (option: DropdownOptionI) => void;
  dropdownClassName?: string;
  buttonClassName?: string;
  optionClassName?: string;
};
