"use client";
import React from "react";
import Button from "../Button";
import { DashboardHeaderPropsI } from "./types";

const DashboardHeader = (props: DashboardHeaderPropsI) => {
  const { title, description, actions } = props;
  return (
    <div className="w-full bg-[#FAFAFA] border-b border-[#D4D4D4] px-6 py-2">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-lg font-semibold text-neutral-900 mb-1">
            {title}
          </h1>
          <p className="text-xs text-neutral-600">{description}</p>
        </div>
        <div className="flex items-center gap-3">
          {actions?.map((action, index) => (
            <Button
              key={index}
              variant={action.variant ?? "ghost"}
              className={action.className}
              onClick={action.onClick}
              disabled={action.disabled ?? false}
            >
              {action.label}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DashboardHeader;
