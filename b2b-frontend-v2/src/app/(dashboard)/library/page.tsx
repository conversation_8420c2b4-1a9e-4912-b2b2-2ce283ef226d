"use client";
import Button from "@/components/Button";
import DashboardHeader from "@/components/DashboardHeader";
import Search from "@/components/Search";
import DropdownButton from "@/components/DropdownButton";
import React from "react";
import { DropdownOptionI } from "@/components/DropdownButton/types";

const LibraryMainPage = () => {
  const addDocumentOptions = [
    {
      id: "create-document",
      label: "Create document",
      onClick: () => console.log("Create document clicked"),
    },
    {
      id: "upload-document",
      label: "Upload document",
      onClick: () => console.log("Upload document clicked"),
    },
  ];

  const handleOptionSelect = (option: DropdownOptionI) => {
    console.log("Selected option:", option);
  };

  return (
    <div className="flex flex-1 flex-col">
      <DashboardHeader
        title="Overview"
        description="This tab contains the information on all the recent intraction of documents with the system"
      />
      <div className="flex justify-between items-center p-6">
        <Search />
        <div className="flex gap-3">
          <DropdownButton
            label="Add Document"
            options={addDocumentOptions}
            variant="primary"
            onOptionSelect={handleOptionSelect}
          />
        </div>
      </div>
    </div>
  );
};

export default LibraryMainPage;
