'use client'
import <PERSON><PERSON> from "@/components/Button";
import DashboardHeader from "@/components/DashboardHeader";
import Search from "@/components/Search";
import React from "react";

const LibraryMainPage = () => {
  return (
    <div className="flex flex-1 flex-col">
      <DashboardHeader
        title="Overview"
        description="This tab contains the information on all the recent intraction of documents with the system"
      />
      <div className="flex justify-between p-6">
        <Search />
        <Button className="w-max">Add Button</Button>
      </div>
    </div>
  )
};

export default LibraryMainPage;
